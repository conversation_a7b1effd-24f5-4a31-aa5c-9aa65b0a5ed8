package com.ainative.mountainsurvival

/**
 * 游戏状态数据类
 * 包含所有核心游戏数值，用于跟踪玩家的生存状态
 */
data class GameState(
    /**
     * 体温 (Warmth)
     * 范围: 0-100
     * 生命线，主要通过壁炉维持。降至0则游戏失败。
     */
    var warmth: Int = 80,

    /**
     * 体力 (Stamina)
     * 范围: 0-100
     * 行动力，执行大部分行动都需要消耗。可通过休息、进食恢复。
     */
    var stamina: Int = 100,

    /**
     * 木柴 (Firewood)
     * 范围: 0-50
     * 热量来源，夜晚壁炉会自动消耗。主要通过外出砍柴获得。
     */
    var firewood: Int = 5,

    /**
     * 食物 (Food)
     * 范围: 0-20
     * 体力来源，可在黄昏阶段选择"进食"来恢复大量体力。
     */
    var food: Int = 3,

    /**
     * 当前天数 (Current Day)
     * 游戏目标是存活7天
     */
    var currentDay: Int = 1,

    /**
     * 房屋状况 (Cabin Integrity) - 隐藏数值
     * 范围: 0-100
     * 代表小屋的坚固程度。某些事件会降低它，玩家需要消耗资源修复。
     */
    var cabinIntegrity: Int = 40,

    /**
     * 希望值 (Hope) - 隐藏数值
     * 范围: 0-100
     * 影响某些事件的文本描述和结局的文本。
     */
    var hope: Int = 50,

    /**
     * 特殊物品
     * 存储玩家获得的特殊物品（如信号枪）
     */
    var specialItems: MutableSet<String> = mutableSetOf(),

    /**
     * 屋顶漏雪状态
     * 如果为true，每晚额外消耗15体温
     */
    var roofLeaking: Boolean = false,

    /**
     * 特殊状态标记
     * 用于记录各种特殊状态和获得的技能
     */
    var specialStates: MutableMap<String, Boolean> = mutableMapOf()
) {

    /**
     * 检查游戏是否结束
     * @return Pair<Boolean, String> - 第一个值表示是否结束，第二个值表示结束原因
     */
    fun checkGameOver(): Pair<Boolean, String> {
        return when {
            warmth <= 0 -> Pair(true, "体温过低")
            stamina <= 0 -> Pair(true, "体力耗尽")
            hope <= 0 -> Pair(true, "绝望而死")
            currentDay > 7 -> Pair(true, getVictoryType())
            else -> Pair(false, "")
        }
    }

    /**
     * 获取胜利类型
     * 根据最终状态决定胜利结局的类型
     */
    private fun getVictoryType(): String {
        return when {
            hope >= 80 && specialItems.contains("signal_gun") -> "完美救援"
            hope >= 70 -> "坚强意志"
            hope >= 40 -> "成功存活"
            else -> "勉强存活"
        }
    }

    /**
     * 应用效果到游戏状态
     * @param effects 效果映射表，键为属性名，值为变化量
     */
    fun applyEffects(effects: Map<String, Int>) {
        effects.forEach { (key, value) ->
            when (key) {
                "warmth" -> warmth = (warmth + value).coerceIn(0, 100)
                "stamina" -> stamina = (stamina + value).coerceIn(0, 100)
                "firewood" -> firewood = (firewood + value).coerceIn(0, 50)
                "food" -> food = (food + value).coerceIn(0, 20)
                "cabin_integrity" -> cabinIntegrity = (cabinIntegrity + value).coerceIn(0, 100)
                "hope" -> hope = (hope + value).coerceIn(0, 100)
            }
        }
    }

    /**
     * 夜晚结算
     * 根据游戏规则自动处理夜晚的数值变化
     */
    fun nightTimeSettlement() {
        // 基础体力消耗增加
        stamina = (stamina - 15).coerceAtLeast(0)

        if (firewood >= 5) {
            // 木柴足够：消耗5木柴，增加体温
            firewood = (firewood - 5).coerceAtLeast(0)
            warmth = (warmth + 8).coerceAtMost(100)
        } else {
            // 木柴不足：体温下降更多
            warmth = (warmth - 25).coerceAtLeast(0)
        }

        // 如果屋顶漏雪，额外消耗体温
        if (roofLeaking) {
            warmth = (warmth - 15).coerceAtLeast(0)
        }

        // 根据小屋状况影响体温保持
        when {
            cabinIntegrity >= 70 -> warmth = (warmth + 5).coerceAtMost(100)
            cabinIntegrity <= 20 -> warmth = (warmth - 10).coerceAtLeast(0)
        }

        // 希望值影响体力恢复
        when {
            hope >= 80 -> stamina = (stamina + 5).coerceAtMost(100)
            hope <= 20 -> stamina = (stamina - 5).coerceAtLeast(0)
        }
    }

    /**
     * 进食
     * 消耗食物，恢复体力
     */
    fun eat(): Boolean {
        return if (food > 0) {
            food -= 1
            stamina = (stamina + 30).coerceAtMost(100)
            // 进食也能稍微提升希望值
            hope = (hope + 3).coerceAtMost(100)
            true
        } else {
            false
        }
    }

    /**
     * 获取状态显示文本
     * @param statName 状态名称
     * @return 格式化的显示文本（包含emoji图标）
     */
    fun getStatusText(statName: String): String {
        return when (statName) {
            "warmth" -> "🔥 $warmth"
            "stamina" -> "💪 $stamina"
            "firewood" -> "🪵 $firewood"
            "food" -> "🍖 $food"
            else -> ""
        }
    }

    /**
     * 设置特殊状态
     */
    fun setSpecialState(stateName: String, value: Boolean) {
        specialStates[stateName] = value
    }

    /**
     * 检查特殊状态
     */
    fun hasSpecialState(stateName: String): Boolean {
        return specialStates[stateName] == true
    }

    /**
     * 获取生存难度评估
     */
    fun getSurvivalDifficulty(): String {
        val totalScore = warmth + stamina + (firewood * 5) + (food * 10) + hope + cabinIntegrity
        return when {
            totalScore >= 400 -> "优秀"
            totalScore >= 300 -> "良好"
            totalScore >= 200 -> "困难"
            totalScore >= 100 -> "危险"
            else -> "绝望"
        }
    }
}
