{"events": [{"id": "day1_start", "text": "刺骨的寒风把你从昏迷中唤醒。你挣扎着睁开眼，发现自己正躺在一间破旧的小木屋里。屋外，暴风雪的呼啸声如同怪物的嘶吼。你检查了一下自己的状况，必须立刻想办法生火！", "choices": [{"text": "劈开旧家具生火", "effects": {"stamina": -15, "firewood": 8, "cabin_integrity": -8}, "resultText": "你用墙角的斧头劈开了一把摇摇欲坠的椅子，冰冷的木屋里终于有了一丝暖意。虽然破坏了一些家具，但现在生存更重要。", "nextEventId": "day1_evening"}, {"text": "冒着风雪出去找柴火", "effects": {"warmth": -25, "stamina": -25, "firewood": 12}, "resultText": "你推开门，几乎被风雪掀翻。你在及膝深的雪地里艰难地收集了一些干树枝，冻僵的手指已经失去了知觉。但你收集到了更多的柴火。", "nextEventId": "day1_evening"}, {"text": "寻找其他热源", "effects": {"stamina": -10, "hope": -5}, "resultText": "你在小屋里四处寻找其他可能的热源，但除了壁炉外什么都没找到。这让你感到有些沮丧，但至少没有浪费太多体力。", "nextEventId": "day1_evening"}]}, {"id": "day1_evening", "text": "黄昏时分，橘红色的火光在壁炉中跳跃着。你感到一阵疲惫，但至少现在有了温暖。夜晚即将来临，你需要为漫长的黑夜做准备。", "choices": [{"text": "加固房屋抵御风雪", "effects": {"stamina": -20, "firewood": -5, "cabin_integrity": 20}, "requirements": {"firewood": 5, "stamina": 20}, "resultText": "你用一些木柴和找到的破布加固了窗户和门缝。虽然消耗了一些资源，但小屋变得更加坚固了。", "nextEventId": "day1_night"}, {"text": "早点休息保存体力", "effects": {"hope": 5}, "resultText": "你决定早点休息。躺在简陋的床上，听着外面的风雪声，你告诉自己一定要坚持下去。", "nextEventId": "day1_night"}, {"text": "检查小屋的安全隐患", "effects": {"stamina": -10, "hope": 3}, "resultText": "你仔细检查了小屋的每个角落，寻找可能的安全隐患。虽然发现了一些小问题，但总体来说还算安全，这让你稍微安心了一些。", "nextEventId": "day1_night"}]}, {"id": "day1_night", "text": "夜幕降临，小屋外的风雪声更加猛烈。壁炉中的火焰是你唯一的光明和温暖来源。你蜷缩在火炉旁，准备度过这个漫长的夜晚。", "effects": {"currentDay": 1}, "choices": [{"text": "继续下一天", "effects": {}, "resultText": "经过一夜的煎熬，你终于看到了第二天的曙光...", "nextEventId": "day2_start"}]}, {"id": "day2_start", "text": "火焰在壁炉里噼啪作响，你活过了第一个夜晚。但你知道，这仅仅是开始。你必须为接下来的日子做准备。透过结霜的窗户，你看到外面依然是白茫茫的一片。", "choices": [{"text": "探索小屋寻找物资", "effects": {"stamina": -15, "currentDay": 1}, "resultText": "你仔细搜索小屋的每个角落。", "nextEventId": "day2_search_result"}, {"text": "外出砍柴", "effects": {"warmth": -20, "stamina": -25, "firewood": 12, "currentDay": 1}, "requirements": {"stamina": 25}, "resultText": "你再次冒着严寒外出砍柴。虽然很辛苦，但你成功收集到了更多的燃料。", "nextEventId": "day2_night"}, {"text": "休息以保存体力", "effects": {"hope": -8, "currentDay": 1}, "resultText": "你决定在屋内休息，虽然没有做什么事情，但至少保存了体力。无所事事让你感到有些沮丧。", "nextEventId": "day2_night"}, {"text": "制作简易工具", "effects": {"stamina": -20, "firewood": -3, "hope": 10, "currentDay": 1}, "requirements": {"stamina": 20, "firewood": 3}, "resultText": "你用一些木材制作了简易的工具，这些工具可能在后续的生存中派上用场。虽然消耗了一些资源，但这让你感到更有准备。", "nextEventId": "day2_night", "specialEffects": {"hasTools": true}}]}, {"id": "day2_search_result", "text": "搜索结果", "choices": [], "conditionalEvents": {"cabin_integrity >= 50": "day2_good_search", "cabin_integrity >= 30": "day2_normal_search", "default": "day2_poor_search"}}, {"id": "day2_good_search", "text": "搜索结果\n\n由于小屋保存得相对完好，你在搜索中有了意外收获！在一个隐藏的储物箱里，你发现了一些罐头食品和一瓶烈酒。", "effects": {"food": 4, "hope": 15, "warmth": 10}, "choices": [{"text": "继续", "effects": {}, "nextEventId": "day2_night"}]}, {"id": "day2_normal_search", "text": "搜索结果\n\n你在小屋里找到了一些基本物资。虽然不多，但总比没有强。", "effects": {"food": 2, "hope": 8}, "choices": [{"text": "继续", "effects": {}, "nextEventId": "day2_night"}]}, {"id": "day2_poor_search", "text": "搜索结果\n\n由于小屋损坏严重，大部分物资都已经被破坏或丢失。你只找到了一些勉强能用的东西。", "effects": {"food": 1, "hope": 3}, "choices": [{"text": "继续", "effects": {}, "nextEventId": "day2_night"}]}, {"id": "day3_start", "text": "第三天 - 呼啸的风\n\n风声变得更加尖锐，仿佛要撕裂这间小屋。你感到一阵不安，屋顶的积雪似乎太厚了。", "choices": [{"text": "加固小屋", "effects": {"stamina": -20, "firewood": -8, "cabin_integrity": 25, "currentDay": 1}, "requirements": {"stamina": 20, "firewood": 8}, "resultText": "你用尽全力加固了小屋的结构，虽然很累，但现在感觉更安全了。", "nextEventId": "day3_evening"}, {"text": "尝试捕猎", "effects": {"warmth": -25, "stamina": -35, "currentDay": 1}, "requirements": {"stamina": 35}, "resultText": "你冒着严寒外出捕猎。", "nextEventId": "day3_hunt_result"}, {"text": "清理屋顶积雪", "effects": {"warmth": -15, "stamina": -25, "cabin_integrity": 10, "currentDay": 1}, "requirements": {"stamina": 25}, "resultText": "你爬上屋顶清理积雪，虽然很危险，但减轻了屋顶的负担。", "nextEventId": "day3_evening"}, {"text": "继续休息", "effects": {"hope": -10, "currentDay": 1}, "resultText": "你选择保存体力，在屋内休息。但无所事事让你感到更加沮丧。", "nextEventId": "day3_evening"}]}, {"id": "day3_hunt_result", "text": "捕猎结果", "choices": [], "conditionalEvents": {"hasTools": "day3_hunt_success", "hope >= 60": "day3_hunt_lucky", "default": "day3_hunt_fail"}}, {"id": "day3_hunt_success", "text": "捕猎结果\n\n由于你之前制作的工具，捕猎变得更加容易！你成功捕获了一只雪兔，获得了丰富的食物。", "effects": {"food": 5, "hope": 15}, "choices": [{"text": "继续", "effects": {}, "nextEventId": "day3_evening"}]}, {"id": "day3_hunt_lucky", "text": "捕猎结果\n\n凭借高昂的斗志和一些运气，你成功捕获了一只小动物！", "effects": {"food": 3, "hope": 10}, "choices": [{"text": "继续", "effects": {}, "nextEventId": "day3_evening"}]}, {"id": "day3_hunt_fail", "text": "捕猎结果\n\n你在雪地里搜寻了很久，但什么都没有找到。严寒和疲劳让你几乎支撑不住。", "effects": {"hope": -15}, "choices": [{"text": "继续", "effects": {}, "nextEventId": "day3_evening"}]}, {"id": "day3_evening", "text": "第三天黄昏，风雪依然猛烈。你需要为即将到来的夜晚做最后的准备。", "choices": [{"text": "制作陷阱准备明天捕猎", "effects": {"stamina": -15, "firewood": -3, "hope": 8}, "requirements": {"stamina": 15, "firewood": 3}, "resultText": "你用木材制作了一个简易陷阱，希望明天能有收获。", "nextEventId": "day3_night", "specialEffects": {"hasTrap": true}}, {"text": "节约资源早点休息", "effects": {"hope": 3}, "resultText": "你决定节约资源，早点休息为明天做准备。", "nextEventId": "day3_night"}]}, {"id": "day3_night", "text": "第三个夜晚来临了。风声更加猛烈，小屋在狂风中摇摆。你紧紧抱着自己，希望能撑过这一夜。外面的暴风雪似乎永远不会停止。", "effects": {"currentDay": 1}, "choices": [{"text": "继续下一天", "effects": {}, "resultText": "第三夜过去了，你依然活着...", "nextEventId": "day4_start"}]}, {"id": "day4_start", "text": "第四天 - 希望的微光\n\n风雪似乎短暂地减弱了。透过窗户的缝隙，你似乎看到了远处有什么东西在反光。", "choices": [{"text": "前往反光点调查", "effects": {"warmth": -30, "stamina": -35, "currentDay": 1}, "requirements": {"stamina": 35}, "resultText": "你决定冒险外出调查那个反光点。深一脚浅一脚地跋涉过去，你发现那是一架小型飞机的残骸！机舱里一片狼藉。", "nextEventId": "crashed_plane"}, {"text": "检查昨天的陷阱", "effects": {"warmth": -10, "stamina": -15, "currentDay": 1}, "requirements": {"hasTrap": true, "stamina": 15}, "resultText": "你去检查昨天设置的陷阱。", "nextEventId": "day4_trap_result"}, {"text": "这是陷阱，待在屋里", "effects": {"hope": -12, "currentDay": 1}, "resultText": "你觉得这可能是陷阱，选择待在屋里。虽然安全，但希望也在减少。", "nextEventId": "day4_indoor_event"}, {"text": "寻找其他生存物资", "effects": {"warmth": -20, "stamina": -25, "currentDay": 1}, "requirements": {"stamina": 25}, "resultText": "你决定在附近寻找其他可能的生存物资。", "nextEventId": "day4_scavenge_result"}]}, {"id": "day4_trap_result", "text": "陷阱检查结果", "choices": [], "randomChoices": [{"probability": 0.6, "nextEventId": "day4_trap_success"}, {"probability": 0.4, "nextEventId": "day4_trap_empty"}]}, {"id": "day4_trap_success", "text": "陷阱检查结果\n\n太好了！陷阱抓到了一只小动物！这给了你继续生存的希望。", "effects": {"food": 2, "hope": 10}, "choices": [{"text": "继续", "effects": {}, "nextEventId": "day4_evening"}]}, {"id": "day4_trap_empty", "text": "陷阱检查结果\n\n陷阱是空的，看起来没有动物经过这里。有些失望，但你可以重新设置它。", "choices": [{"text": "重新设置陷阱", "effects": {"stamina": -10, "hope": 5}, "resultText": "你重新设置了陷阱，希望明天会有更好的运气。", "nextEventId": "day4_evening", "specialEffects": {"hasTrap": true}}, {"text": "放弃陷阱", "effects": {"hope": -3}, "resultText": "你决定放弃陷阱，专注于其他生存方式。", "nextEventId": "day4_evening"}]}, {"id": "day4_indoor_event", "text": "你决定待在室内，但突然听到了奇怪的声音...", "choices": [], "randomChoices": [{"probability": 0.3, "nextEventId": "day4_roof_collapse", "effects": {"cabin_integrity": -25, "warmth": -15, "hope": -10}, "resultText": "屋顶的一部分突然塌陷了！雪花飘进屋内，情况变得更加糟糕。"}, {"probability": 0.7, "nextEventId": "day4_false_alarm", "effects": {"hope": 3}, "resultText": "原来只是风声造成的错觉。虽然虚惊一场，但至少没有发生什么坏事。"}]}, {"id": "day4_roof_collapse", "text": "屋顶部分塌陷！你必须立即采取行动。", "choices": [{"text": "紧急修复", "effects": {"stamina": -25, "firewood": -10, "cabin_integrity": 15}, "requirements": {"stamina": 25, "firewood": 10}, "resultText": "你拼尽全力进行紧急修复，虽然消耗巨大，但阻止了进一步的损坏。", "nextEventId": "day4_evening"}, {"text": "暂时用布料遮挡", "effects": {"stamina": -10, "cabin_integrity": 5}, "resultText": "你用找到的布料暂时遮挡破洞，这只是权宜之计。", "nextEventId": "day4_evening"}]}, {"id": "day4_false_alarm", "text": "虚惊一场，但这提醒你要时刻保持警惕。", "choices": [{"text": "继续", "effects": {}, "nextEventId": "day4_evening"}]}, {"id": "day4_scavenge_result", "text": "搜寻物资的结果", "choices": [], "conditionalEvents": {"hope >= 50": "day4_scavenge_success", "hope >= 30": "day4_scavenge_partial", "default": "day4_scavenge_fail"}}, {"id": "day4_scavenge_success", "text": "搜寻物资的结果\n\n凭借坚定的意志，你在雪地里找到了一些有用的物资！", "effects": {"food": 2, "firewood": 5, "hope": 8}, "choices": [{"text": "继续", "effects": {}, "nextEventId": "day4_evening"}]}, {"id": "day4_scavenge_partial", "text": "搜寻物资的结果\n\n你找到了一些基本物资，虽然不多但总比没有强。", "effects": {"food": 1, "firewood": 3, "hope": 3}, "choices": [{"text": "继续", "effects": {}, "nextEventId": "day4_evening"}]}, {"id": "day4_scavenge_fail", "text": "搜寻物资的结果\n\n你什么都没找到，白白消耗了体力和体温。沮丧的情绪开始蔓延。", "effects": {"hope": -8}, "choices": [{"text": "继续", "effects": {}, "nextEventId": "day4_evening"}]}, {"id": "day4_evening", "text": "第四天黄昏，你需要为夜晚做准备。今天的经历让你对明天有了新的计划。", "choices": [{"text": "准备明天的行动", "effects": {"stamina": -5, "hope": 5}, "resultText": "你仔细规划明天的行动，这让你感到更有准备。", "nextEventId": "day4_night"}, {"text": "保存体力", "effects": {}, "resultText": "你决定保存体力，为明天的挑战做准备。", "nextEventId": "day4_night"}]}, {"id": "crashed_plane", "text": "坠毁的飞机\n\n你深一脚浅一脚地跋涉过去，发现那是一架小型飞机的残骸！机舱里一片狼藉。", "choices": [{"text": "搜索驾驶舱", "effects": {}, "resultText": "你仔细搜索驾驶舱，在座椅下面找到了一把信号枪！这可能是你获救的关键。", "specialItemGained": "signal_gun", "nextEventId": "day4_night_with_gun"}, {"text": "搜索货仓", "effects": {"food": 3}, "resultText": "你搜索了货仓，发现了一些应急食品！虽然包装有些破损，但还是可以食用的。", "nextEventId": "day4_night_no_gun"}]}, {"id": "day4_night_with_gun", "text": "第四个夜晚。今天的发现让你对明天充满了期待，但现在你必须先撑过这一夜。你握着手中的信号枪，这给了你一丝希望。", "effects": {"currentDay": 1}, "choices": [{"text": "继续下一天", "effects": {}, "resultText": "第四夜过去了，你离救援更近了...", "nextEventId": "day5_start"}]}, {"id": "day4_night_no_gun", "text": "第四个夜晚。今天的发现给了你一些物资，但现在你必须先撑过这一夜。虽然没有找到求救工具，但至少有了更多的食物。", "effects": {"currentDay": 1}, "choices": [{"text": "继续下一天", "effects": {}, "resultText": "第四夜过去了，你继续坚持着...", "nextEventId": "day5_start"}]}, {"id": "day4_night", "text": "第四个夜晚。今天的经历让你对生存有了新的认识，但现在你必须先撑过这一夜。", "effects": {"currentDay": 1}, "choices": [{"text": "继续下一天", "effects": {}, "resultText": "第四夜过去了，你依然在坚持...", "nextEventId": "day5_start"}]}, {"id": "day2_night", "text": "第二个夜晚来临了。你已经更加熟悉这间小屋，也更加了解如何在这里生存。壁炉中的火焰依然温暖，但你知道每一夜都是对生存意志的考验。", "effects": {"currentDay": 1}, "choices": [{"text": "继续下一天", "effects": {}, "resultText": "又一个夜晚过去了，你离救援又近了一步...", "nextEventId": "day3_start"}]}, {"id": "day5_start", "text": "第五天 - 绝望\n\n风雪再次加强，比以往任何时候都要猛烈。小屋在狂风中颤抖，你感到一阵深深的绝望。", "choices": [{"text": "检查房屋状况", "effects": {"stamina": -15}, "resultText": "你仔细检查了房屋的状况...", "nextEventId": "house_check_result"}, {"text": "蜷缩在壁炉边", "effects": {"hope": -8, "currentDay": 1}, "resultText": "你选择蜷缩在壁炉边取暖，虽然身体暖和了一些，但心情更加沮丧。", "nextEventId": "day5_evening"}, {"text": "强迫自己外出寻找希望", "effects": {"warmth": -30, "stamina": -30, "currentDay": 1}, "requirements": {"stamina": 30, "hope": 20}, "resultText": "你强迫自己走出小屋，在暴风雪中寻找任何可能的希望...", "nextEventId": "day5_desperate_search"}, {"text": "整理物资评估情况", "effects": {"stamina": -10, "hope": 5, "currentDay": 1}, "resultText": "你冷静地整理所有物资，评估当前的生存状况。虽然情况不乐观，但至少心中有数。", "nextEventId": "day5_evening"}]}, {"id": "day5_desperate_search", "text": "绝望的搜寻", "choices": [], "randomChoices": [{"probability": 0.2, "nextEventId": "day5_find_shelter", "effects": {"hope": 25, "cabin_integrity": 20}, "resultText": "奇迹般地，你发现了一个废弃的猎人小屋！虽然简陋，但可以作为备用避难所。"}, {"probability": 0.3, "nextEventId": "day5_find_supplies", "effects": {"food": 3, "firewood": 8, "hope": 15}, "resultText": "在一个雪堆下，你发现了一些被遗弃的补给品！这些物资可能救你一命。"}, {"probability": 0.5, "nextEventId": "day5_nothing_found", "effects": {"hope": -15}, "resultText": "你在暴风雪中搜寻了很久，但什么都没找到。绝望感更加强烈了。"}]}, {"id": "day5_find_shelter", "text": "你发现了一个废弃的猎人小屋！这给了你新的希望和选择。", "choices": [{"text": "立即搬到新小屋", "effects": {"stamina": -20, "hope": 10}, "resultText": "你决定立即搬到新发现的小屋。虽然搬迁很累，但新环境让你感到更安全。", "nextEventId": "day5_evening", "specialEffects": {"hasBackupShelter": true}}, {"text": "记住位置，暂时回去", "effects": {"hope": 5}, "resultText": "你记住了小屋的位置，决定先回到原来的地方。至少现在有了备选方案。", "nextEventId": "day5_evening", "specialEffects": {"knowsBackupShelter": true}}]}, {"id": "day5_find_supplies", "text": "意外的收获！这些补给品让你的生存状况大大改善。", "choices": [{"text": "继续", "effects": {}, "nextEventId": "day5_evening"}]}, {"id": "day5_nothing_found", "text": "一无所获的搜寻让你更加绝望，但至少你还在努力。", "choices": [{"text": "继续", "effects": {}, "nextEventId": "day5_evening"}]}, {"id": "day5_evening", "text": "第五天黄昏，你需要做出一些重要的决定来度过接下来的困难时期。", "choices": [{"text": "制定详细的生存计划", "effects": {"stamina": -10, "hope": 10}, "requirements": {"hope": 30}, "resultText": "你冷静地制定了详细的生存计划，这让你感到更有控制力。", "nextEventId": "day5_night"}, {"text": "节约所有资源", "effects": {"hope": 5}, "resultText": "你决定严格节约所有资源，为最坏的情况做准备。", "nextEventId": "day5_night"}, {"text": "如果有备用小屋，考虑搬迁", "effects": {"stamina": -25, "hope": 15}, "requirements": {"knowsBackupShelter": true, "stamina": 25}, "resultText": "你决定搬到之前发现的备用小屋，这是一个艰难但明智的决定。", "nextEventId": "day5_night", "specialEffects": {"hasBackupShelter": true}}]}, {"id": "house_check_result", "text": "检查房屋状况结果", "choices": [], "conditionalEvents": {"cabin_integrity < 50": "roof_leak", "default": "house_ok"}}, {"id": "house_ok", "text": "检查房屋状况结果\n\n幸运的是，房屋状况还算良好，没有发现严重的问题。", "choices": [{"text": "继续", "effects": {}, "nextEventId": "day5_evening"}]}, {"id": "roof_leak", "text": "检查房屋状况结果\n\n屋顶的一角开始漏下冰冷的雪水，很快就会让你的体温迅速流失！", "choices": [{"text": "立刻修复", "effects": {"stamina": -20, "firewood": -10, "cabin_integrity": 15, "currentDay": 1}, "requirements": {"stamina": 20, "firewood": 10}, "resultText": "你立刻动手修复屋顶，虽然消耗了大量体力和木柴，但阻止了进一步的损坏。", "nextEventId": "day5_evening"}, {"text": "暂时不管", "effects": {"hope": -10, "currentDay": 1}, "resultText": "你决定暂时不管漏雪的问题，但这意味着每晚你都会损失更多体温。", "nextEventId": "day5_evening", "specialEffects": {"roofLeaking": true}}]}, {"id": "day5_night", "text": "第五个夜晚。风雪依然猛烈，但你的意志依然坚强。", "effects": {"currentDay": 1}, "choices": [{"text": "继续生存之旅", "effects": {}, "resultText": "第五夜过去了，你还在坚持...", "nextEventId": "day6_start"}]}, {"id": "day6_start", "text": "第六天 - 漫长的夜晚\n\n这可能是最后一个夜晚了，也可能是你人生的最后一个夜晚。你必须撑过去。", "choices": [{"text": "把所有能烧的都烧了", "effects": {"firewood": 15, "cabin_integrity": -25, "hope": 8}, "resultText": "你把所有能烧的东西都当作燃料，包括一些家具。虽然破坏了房屋，但获得了大量木柴和一丝希望。", "nextEventId": "day6_evening"}, {"text": "做最后的准备", "effects": {"stamina": 5, "hope": 5}, "resultText": "你冷静地为可能的最后一夜做准备，保存体力。", "nextEventId": "day6_evening"}, {"text": "尝试最后的求救", "effects": {"stamina": -25, "hope": 15}, "requirements": {"stamina": 25}, "resultText": "你用尽最后的力气尝试各种求救方法...", "nextEventId": "day6_rescue_attempt"}, {"text": "如果有备用小屋，立即转移", "effects": {"stamina": -30, "hope": 20}, "requirements": {"hasBackupShelter": true, "stamina": 30}, "resultText": "你决定转移到备用小屋，这可能是最后的机会了。", "nextEventId": "day6_evening", "specialEffects": {"inBackupShelter": true}}]}, {"id": "day6_rescue_attempt", "text": "最后的求救尝试", "choices": [], "conditionalEvents": {"hope >= 70": "day6_rescue_success", "hope >= 40": "day6_rescue_partial", "default": "day6_rescue_fail"}}, {"id": "day6_rescue_success", "text": "凭借坚定的意志，你成功制作了一个巨大的求救信号！这给了你巨大的希望。", "effects": {"hope": 25}, "choices": [{"text": "继续", "effects": {}, "nextEventId": "day6_evening"}]}, {"id": "day6_rescue_partial", "text": "你尽力制作了求救信号，虽然不够完美，但总比什么都不做强。", "effects": {"hope": 10}, "choices": [{"text": "继续", "effects": {}, "nextEventId": "day6_evening"}]}, {"id": "day6_rescue_fail", "text": "你的求救尝试失败了，这让你更加绝望。但至少你还在努力。", "effects": {"hope": -5}, "choices": [{"text": "继续", "effects": {}, "nextEventId": "day6_evening"}]}, {"id": "day6_evening", "text": "第六天黄昏\n\n太阳即将落下，这可能是你看到的最后一次日落。但你不会放弃。", "choices": [{"text": "仔细检查所有装备", "effects": {"stamina": -5, "hope": 5}, "resultText": "你仔细检查了所有的装备和物资，确保一切都准备就绪。这让你感到更有信心。", "nextEventId": "day6_night"}, {"text": "节约体力静静等待", "effects": {"hope": -5}, "resultText": "你选择静静地坐着，节约体力。虽然保存了力气，但内心的焦虑在增长。", "nextEventId": "day6_night"}, {"text": "向外面大声呼救", "effects": {"stamina": -15, "hope": 10}, "resultText": "你打开门向外面大声呼救，虽然消耗了体力，但这让你感到至少还在努力争取生存。", "nextEventId": "day6_night"}, {"text": "制作紧急求救标志", "effects": {"stamina": -20, "firewood": -8, "hope": 20}, "requirements": {"hope": 60, "stamina": 20, "firewood": 8}, "resultText": "凭借高昂的斗志，你用木柴在雪地上制作了一个巨大的SOS标志。这个创意给了你巨大的希望！", "nextEventId": "day6_night"}]}, {"id": "day6_night", "text": "第六个夜晚。这是最漫长、最寒冷的一夜。你紧紧抱着自己，等待黎明的到来。", "effects": {"currentDay": 1}, "choices": [{"text": "保持清醒警戒", "effects": {"stamina": -10, "hope": 5}, "resultText": "你强迫自己保持清醒，时刻警戒着周围的动静。虽然很累，但这让你感到更安全。第六夜过去了，你奇迹般地活了下来...", "nextEventId": "day7_start"}, {"text": "尽量多睡觉恢复体力", "effects": {"stamina": 5, "hope": -5}, "resultText": "你尽量让自己多睡一些，恢复体力。虽然恢复了一些力气，但噩梦不断。第六夜过去了，你奇迹般地活了下来...", "nextEventId": "day7_start"}, {"text": "回忆美好的往事", "effects": {"hope": 10}, "resultText": "你闭上眼睛，回忆着家人朋友和美好的往事。这些温暖的回忆给了你继续坚持的力量。第六夜过去了，你奇迹般地活了下来...", "nextEventId": "day7_start"}]}, {"id": "day7_start", "text": "第七天 - 最后的坚持", "choices": [], "conditionalEvents": {"hope <= 20": "day7_despair", "hope >= 70": "day7_hope", "default": "day7_normal"}}, {"id": "day7_despair", "text": "第七天 - 绝望的边缘\n\n你几乎已经放弃了所有希望。眼神空洞地望着窗外的风雪，内心一片死寂。也许...这就是结束了...", "choices": [{"text": "放弃挣扎", "effects": {"hope": -30, "stamina": -20}, "resultText": "你彻底放弃了。绝望吞噬了你的内心，你不再有任何求生的意志...", "nextEventId": "day7_evening"}, {"text": "最后的挣扎", "effects": {"hope": 10, "stamina": -10}, "resultText": "即使在绝望中，你仍然选择挣扎。也许这就是人类的本能...", "nextEventId": "day7_evening"}, {"text": "如果有信号枪，向天空发射", "effects": {"hope": 50}, "requirements": {"special_items": ["signal_gun"]}, "resultText": "在绝望中，你想起了信号枪！这是最后的希望！红色的光芒划破天际！", "specialItemUsed": "signal_gun", "nextEventId": "rescue_ending"}]}, {"id": "day7_hope", "text": "第七天 - 希望之光\n\n虽然已经是第七天，但你的内心依然充满希望。你相信自己一定能够坚持到最后！", "choices": [{"text": "充满信心地等待", "effects": {"hope": 10, "stamina": 5}, "resultText": "你的乐观精神给了你力量。即使在困境中，你依然保持着积极的心态。", "nextEventId": "day7_evening"}, {"text": "准备迎接救援", "effects": {"hope": 15, "stamina": -5}, "resultText": "你开始为可能到来的救援做准备，整理好所有物品，保持最佳状态。", "nextEventId": "day7_evening"}, {"text": "如果有信号枪，向天空发射", "effects": {"hope": 50}, "requirements": {"special_items": ["signal_gun"]}, "resultText": "满怀希望地，你向天空发射了信号弹！红色的光芒如希望之星般闪耀！", "specialItemUsed": "signal_gun", "nextEventId": "rescue_ending"}]}, {"id": "day7_normal", "text": "第七天 - 最后的坚持\n\n你几乎已经麻木了。生存的本能驱使着你睁开眼睛。窗外依旧是白茫茫的一片。", "choices": [{"text": "等待", "effects": {}, "resultText": "你选择静静等待，保存最后的力气。", "nextEventId": "day7_evening"}, {"text": "如果有信号枪，向天空发射", "effects": {"hope": 50}, "requirements": {"special_items": ["signal_gun"]}, "resultText": "你拿出信号枪，向天空发射了一发信号弹！红色的光芒划破天际，这是你最后的希望！", "specialItemUsed": "signal_gun", "nextEventId": "rescue_ending"}]}, {"id": "day7_evening", "text": "第七天黄昏\n\n又一天过去了。你不知道还能坚持多久，但你依然活着。这是最关键的时刻。", "choices": [{"text": "制作求救信号", "effects": {"stamina": -15, "firewood": -5, "hope": 15}, "requirements": {"stamina": 15, "firewood": 5}, "resultText": "你用剩余的木柴制作了一个大型的求救信号，在雪地上摆成SOS的形状。这消耗了你的体力，但给了你巨大的希望。", "nextEventId": "day7_night"}, {"text": "写下遗言", "effects": {"stamina": -5, "hope": -10}, "resultText": "你在一张纸上写下了给家人的遗言，以防万一。虽然让你感到悲伤，但也让你更加珍惜生命。", "nextEventId": "day7_night"}, {"text": "坚定信念继续等待", "effects": {"hope": 5}, "resultText": "你坚定地告诉自己，救援一定会来的。这种信念给了你继续坚持的力量。", "nextEventId": "day7_night"}]}, {"id": "day7_night", "text": "第七个夜晚。你已经在这里生存了一周。无论结果如何，你都已经证明了自己的坚强。这是最后的考验。", "effects": {"currentDay": 1}, "choices": [{"text": "燃烧最后的希望", "effects": {"firewood": -10, "warmth": 20, "hope": 20}, "requirements": {"firewood": 10}, "resultText": "你决定燃烧剩余的所有木柴，让火焰燃烧得更旺。温暖的火光照亮了整个小屋，也照亮了你的希望。第七夜过去了，你依然在等待...", "nextEventId": "survival_ending"}, {"text": "默默祈祷", "effects": {"hope": 10}, "resultText": "你闭上眼睛，默默祈祷着能够度过这最后的夜晚。内心的平静给了你力量。第七夜过去了，你依然在等待...", "nextEventId": "survival_ending"}, {"text": "坚持到底", "effects": {"stamina": -5, "hope": 5}, "resultText": "你咬紧牙关，告诉自己无论如何都要坚持到底。这种坚定的意志支撑着你度过了最后的夜晚。第七夜过去了，你依然在等待...", "nextEventId": "survival_ending"}]}, {"id": "rescue_ending", "text": "救援结局\n\n几个小时后，你听到了直升机的声音！信号枪起作用了！救援队找到了你，你获救了！", "choices": [{"text": "重新开始游戏", "effects": {}, "nextEventId": "day1_start"}]}, {"id": "survival_ending", "text": "生存结局\n\n你在雪山中坚持了七天七夜。虽然救援还没有到来，但你证明了人类的坚韧不拔。故事还在继续...", "choices": [{"text": "重新开始游戏", "effects": {}, "nextEventId": "day1_start"}]}, {"id": "perfect_survival_ending", "text": "完美生存结局\n\n你不仅在雪山中坚持了七天，还保持了良好的身体和精神状态。你的智慧和勇气让你成为了真正的生存专家！", "choices": [{"text": "重新开始游戏", "effects": {}, "nextEventId": "day1_start"}]}, {"id": "desperate_ending", "text": "绝望结局\n\n虽然你坚持了七天，但绝望已经吞噬了你的内心。你勉强活着，但已经失去了对未来的希望...", "choices": [{"text": "重新开始游戏", "effects": {}, "nextEventId": "day1_start"}]}, {"id": "shelter_collapse_ending", "text": "避难所崩塌结局\n\n小屋最终无法承受暴风雪的摧残而倒塌。你在废墟中艰难求生，但最终还是坚持到了第七天...", "choices": [{"text": "重新开始游戏", "effects": {}, "nextEventId": "day1_start"}]}, {"id": "random_event_wolf", "text": "狼群威胁\n\n夜晚，你听到了狼嚎声。一群饥饿的狼正在接近小屋！", "choices": [{"text": "用火把驱赶", "effects": {"firewood": -8, "hope": 10}, "requirements": {"firewood": 8}, "resultText": "你点燃火把挥舞着驱赶狼群。火光让狼群退却了，但消耗了不少木柴。", "nextEventId": "continue_day"}, {"text": "紧闭门窗躲避", "effects": {"hope": -10, "cabin_integrity": -10}, "resultText": "你紧闭门窗躲在屋内。狼群在外面徘徊了很久，还抓挠了门窗，让你感到恐惧。", "nextEventId": "continue_day"}, {"text": "大声呼喊恐吓", "effects": {"stamina": -15, "hope": 5}, "resultText": "你大声呼喊试图恐吓狼群。虽然消耗了体力，但狼群似乎被你的勇气震慑了。", "nextEventId": "continue_day"}]}, {"id": "random_event_blizzard", "text": "超级暴风雪\n\n一场前所未有的暴风雪袭来！风力强到几乎要掀翻小屋。", "choices": [{"text": "加固所有门窗", "effects": {"stamina": -20, "firewood": -10, "cabin_integrity": 15}, "requirements": {"stamina": 20, "firewood": 10}, "resultText": "你拼命加固所有门窗。虽然很累，但小屋在暴风雪中坚持住了。", "nextEventId": "continue_day"}, {"text": "躲在最安全的角落", "effects": {"warmth": -15, "hope": -8}, "resultText": "你躲在小屋最安全的角落。暴风雪肆虐了整夜，你冻得瑟瑟发抖。", "nextEventId": "continue_day"}]}, {"id": "random_event_injury", "text": "意外受伤\n\n你在活动时不小心被锋利的木片划伤了！伤口在流血。", "choices": [{"text": "用雪止血包扎", "effects": {"warmth": -10, "stamina": -5, "hope": 5}, "resultText": "你用雪止血并撕下衣服包扎伤口。虽然简陋，但至少止住了血。", "nextEventId": "continue_day"}, {"text": "用火烧灼伤口", "effects": {"stamina": -15, "firewood": -3, "hope": 8}, "requirements": {"firewood": 3}, "resultText": "你忍着剧痛用火烧灼伤口消毒。虽然痛苦，但这是最有效的处理方法。", "nextEventId": "continue_day"}, {"text": "忽视伤口继续活动", "effects": {"stamina": -10, "hope": -10}, "resultText": "你选择忽视伤口继续活动。伤口持续流血，让你感到虚弱和担忧。", "nextEventId": "continue_day"}]}, {"id": "random_event_food_spoiled", "text": "食物变质\n\n你发现一部分储存的食物已经变质了！在这种环境下，食物保存确实很困难。", "effects": {"food": -2, "hope": -8}, "choices": [{"text": "继续", "effects": {}, "nextEventId": "continue_day"}]}, {"id": "random_event_find_diary", "text": "发现日记\n\n你在小屋的角落里发现了一本日记，是之前住在这里的人留下的。", "choices": [{"text": "仔细阅读日记", "effects": {"stamina": -5, "hope": 15}, "resultText": "日记记录了前人的生存经验和技巧，这些知识对你很有帮助！", "nextEventId": "continue_day", "specialEffects": {"hasSurvivalKnowledge": true}}, {"text": "简单翻阅", "effects": {"hope": 8}, "resultText": "你简单翻阅了日记，虽然没有深入研究，但知道有人曾经在这里生存过让你感到安慰。", "nextEventId": "continue_day"}, {"text": "不看日记", "effects": {"hope": -3}, "resultText": "你选择不看日记，但这让你错过了可能的有用信息。", "nextEventId": "continue_day"}]}, {"id": "continue_day", "text": "事件结束，继续当天的活动。", "choices": [{"text": "继续", "effects": {}, "resultText": "你重新专注于生存，继续面对这个严酷的环境。"}]}]}