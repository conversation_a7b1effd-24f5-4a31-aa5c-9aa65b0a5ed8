package com.ainative.mountainsurvival

import android.content.Context
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.io.IOException

/**
 * 事件引擎类
 * 负责从JSON文件加载和管理游戏事件
 */
class EventEngine(private val context: Context) {

    private var eventContainer: EventContainer? = null
    private val gson = Gson()

    /**
     * 从assets文件夹加载事件数据
     * @return 是否加载成功
     */
    fun loadEvents(): Boolean {
        return try {
            val jsonString = loadJsonFromAssets("events.json")
            eventContainer = gson.fromJson(jsonString, EventContainer::class.java)
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 根据ID获取事件
     * @param eventId 事件ID
     * @return 对应的游戏事件，如果不存在则返回null
     */
    fun getEvent(eventId: String): GameEvent? {
        return eventContainer?.events?.find { it.id == eventId }
    }

    /**
     * 获取所有事件
     * @return 事件列表
     */
    fun getAllEvents(): List<GameEvent> {
        return eventContainer?.events ?: emptyList()
    }

    /**
     * 检查事件是否存在
     * @param eventId 事件ID
     * @return 是否存在
     */
    fun hasEvent(eventId: String): Boolean {
        return getEvent(eventId) != null
    }

    /**
     * 获取事件数量
     * @return 事件总数
     */
    fun getEventCount(): Int {
        return eventContainer?.events?.size ?: 0
    }

    /**
     * 验证所有事件的完整性
     * @return 验证结果列表，包含所有发现的问题
     */
    fun validateAllEvents(): List<String> {
        val issues = mutableListOf<String>()

        eventContainer?.events?.forEach { event ->
            if (!GameEventUtils.validateEvent(event)) {
                issues.add("事件 ${event.id} 验证失败")
            }

            // 检查nextEventId是否存在
            event.choices.forEach { choice ->
                choice.nextEventId?.let { nextId ->
                    if (!hasEvent(nextId)) {
                        issues.add("事件 ${event.id} 的选择 '${choice.text}' 引用了不存在的事件: $nextId")
                    }
                }
            }

            // 检查随机事件ID是否存在
            event.randomChoices?.forEach { randomChoice ->
                if (!hasEvent(randomChoice.nextEventId)) {
                    issues.add("事件 ${event.id} 的随机选择引用了不存在的事件: ${randomChoice.nextEventId}")
                }
            }
        }

        return issues
    }

    /**
     * 获取可用的选择
     * 根据游戏状态过滤出玩家可以选择的选项
     * @param event 当前事件
     * @param gameState 当前游戏状态
     * @return 可用的选择列表
     */
    fun getAvailableChoices(event: GameEvent, gameState: GameState): List<Choice> {
        return event.choices.filter { choice ->
            GameEventUtils.isChoiceAvailable(choice, gameState)
        }
    }

    /**
     * 处理随机事件
     * @param event 包含随机选择的事件
     * @return 选中的随机选择，如果没有随机选择则返回null
     */
    fun processRandomEvent(event: GameEvent): RandomChoice? {
        return event.randomChoices?.let { randomChoices ->
            GameEventUtils.selectRandomEvent(randomChoices)
        }
    }

    /**
     * 从assets文件夹读取JSON文件
     * @param fileName 文件名
     * @return JSON字符串
     */
    private fun loadJsonFromAssets(fileName: String): String {
        return try {
            val inputStream = context.assets.open(fileName)
            val size = inputStream.available()
            val buffer = ByteArray(size)
            inputStream.read(buffer)
            inputStream.close()
            String(buffer, Charsets.UTF_8)
        } catch (ex: IOException) {
            ex.printStackTrace()
            ""
        }
    }

    /**
     * 获取事件统计信息
     * @return 包含各种统计数据的Map
     */
    fun getEventStatistics(): Map<String, Any> {
        val events = getAllEvents()
        val totalChoices = events.sumOf { it.choices.size }
        val eventsWithRandomChoices = events.count { !it.randomChoices.isNullOrEmpty() }
        val eventsWithEffects = events.count { !it.effects.isNullOrEmpty() }

        return mapOf(
            "totalEvents" to events.size,
            "totalChoices" to totalChoices,
            "averageChoicesPerEvent" to if (events.isNotEmpty()) totalChoices.toDouble() / events.size else 0.0,
            "eventsWithRandomChoices" to eventsWithRandomChoices,
            "eventsWithDirectEffects" to eventsWithEffects
        )
    }

    /**
     * 搜索包含特定文本的事件
     * @param searchText 搜索文本
     * @return 匹配的事件列表
     */
    fun searchEvents(searchText: String): List<GameEvent> {
        val lowerSearchText = searchText.lowercase()
        return getAllEvents().filter { event ->
            event.text.lowercase().contains(lowerSearchText) ||
            event.choices.any { it.text.lowercase().contains(lowerSearchText) }
        }
    }
}
